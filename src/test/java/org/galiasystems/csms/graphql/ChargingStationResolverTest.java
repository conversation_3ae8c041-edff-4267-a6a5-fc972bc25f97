package org.galiasystems.csms.graphql;

import io.netty.handler.codec.http.HttpResponseStatus;
import io.quarkus.test.TestReactiveTransaction;
import io.quarkus.test.junit.QuarkusTest;
import io.quarkus.test.vertx.UniAsserter;
import io.restassured.http.ContentType;
import io.smallrye.mutiny.Uni;
import io.vertx.core.http.HttpMethod;
import io.vertx.mutiny.core.Vertx;
import io.vertx.mutiny.core.buffer.Buffer;
import io.vertx.mutiny.ext.web.client.WebClient;
import jakarta.inject.Inject;
import jakarta.transaction.SystemException;
import org.galiasystems.csms.management.model.enums.AvailabilityStatus;
import org.galiasystems.csms.management.model.enums.ChargingStationStatus;
import org.galiasystems.csms.management.model.enums.EvseStatus;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import static io.restassured.RestAssured.given;
import static org.hamcrest.CoreMatchers.*;
import static org.hamcrest.Matchers.greaterThanOrEqualTo;
import static org.hamcrest.Matchers.hasSize;

@QuarkusTest
public class ChargingStationResolverTest {

    private static final String GRAPHQL_ENDPOINT = "/graphql";

    private static final String CHARGING_STATION_BASIC_QUERY = """
            {
              "query": "query {
                chargingStation(id: %d) {
                  id
                  name
                  status
                  availabilityStatus
                }
              }"
            }""";

    private static final String CHARGING_STATION_WITH_EVSES_QUERY = """
            {
              "query": "query {
                chargingStation(id: %d) {
                  id
                  name
                  evses {
                    id
                    chargingStationEvseId
                    status
                    availabilityStatus
                  }
                }
              }"
            }""";

    private static final String CHARGING_STATION_SIMPLE_QUERY = """
            {
              "query": "query {
                chargingStation(id: %d) {
                  id
                  name
                }
              }"
            }""";

    private static final String CHARGING_STATIONS_BASIC_QUERY = """
            {
              "query": "query {
                chargingStations {
                  id
                  name
                  status
                  availabilityStatus
                }
              }"
            }""";

    private static final String CHARGING_STATIONS_WITH_FILTER_QUERY = """
            {
              "query": "query {
                chargingStations(filter: {%s}) {
                  id
                  name
                  status
                  availabilityStatus
                }
              }"
            }""";

    private static final String CHARGING_STATIONS_WITH_NESTED_QUERY = """
            {
              "query": "query {
                chargingStations {
                  id
                  name
                  status
                  availabilityStatus
                  reports {
                    id
                    requestId
                    status
                    type
                    responseStatus
                  }
                  chargingStationVariables {
                    id
                    componentName
                    variableName
                    chargingStationVariableValues {
                      id
                      type
                      value
                      mutability
                    }
                  }
                  evses {
                    id
                    chargingStationEvseId
                    status
                    availabilityStatus
                  }
                }
              }"
            }""";

    private static final String REGISTER_CHARGING_STATION_MUTATION = """
            {
              "query": "mutation {
                registerChargingStation(name: \\"%s\\") {
                  id
                  name
                  userName
                  password
                  status
                  availabilityStatus
                  url
                }
              }"
            }""";

    private static final String UPDATE_CHARGING_STATION_MUTATION = """
            {
              "query": "mutation {
                updateChargingStation(chargingStationId: %d, name: \\"%s\\") {
                  id
                  name
                  status
                  availabilityStatus
                  url
                  userName
                  password
                }
              }"
            }""";

    private static final String UPDATE_CHARGING_STATION_MUTATION_NULL_NAME = """
            {
              "query": "mutation {
                updateChargingStation(chargingStationId: %d) {
                  id
                  name
                  status
                  availabilityStatus
                  url
                  userName
                  password
                }
              }"
            }""";

    @Test
    @DisplayName("Should return charging station with basic fields")
    public void testGetChargingStationQuery() {
        Integer existingChargingStationId = -1;
        given()
                .contentType(ContentType.JSON)
                .body(String.format(CHARGING_STATION_BASIC_QUERY, existingChargingStationId))
                .when()
                .post(GRAPHQL_ENDPOINT)
                .then()
                .statusCode(HttpResponseStatus.OK.code())
                .body("errors", nullValue())
                .body("data.chargingStation", notNullValue())
                .body("data.chargingStation.id", is(existingChargingStationId.toString()))
                .body("data.chargingStation.name", is("CP0"))
                .body("data.chargingStation.status", is(ChargingStationStatus.Offline.name()))
                .body("data.chargingStation.availabilityStatus", is(AvailabilityStatus.Unknown.name()));
    }

    @Test
    @DisplayName("Should return null for non-existing charging station")
    public void testGetChargingStationQueryNotFound() {
        given()
                .contentType(ContentType.JSON)
                .body(String.format(CHARGING_STATION_SIMPLE_QUERY, 999999))
                .when()
                .post(GRAPHQL_ENDPOINT)
                .then()
                .statusCode(HttpResponseStatus.OK.code())
                .body("errors", nullValue())
                .body("data.chargingStation", nullValue());
    }

    @Test
    @DisplayName("Should return charging station with evse details")
    public void testGetChargingStationWithEvses() {
        Integer existingChargingStationId = -1;
        given()
                .contentType(ContentType.JSON)
                .body(String.format(CHARGING_STATION_WITH_EVSES_QUERY, existingChargingStationId))
                .when()
                .post(GRAPHQL_ENDPOINT)
                .then()
                .statusCode(HttpResponseStatus.OK.code())
                .body("errors", nullValue())
                .body("data.chargingStation", notNullValue())
                .body("data.chargingStation.id", is(existingChargingStationId.toString()))
                .body("data.chargingStation.name", is("CP0"))
                .body("data.chargingStation.evses", hasSize(1))
                .body("data.chargingStation.evses[0].id", is("-1"))
                .body("data.chargingStation.evses[0].chargingStationEvseId", is(1))
                .body("data.chargingStation.evses[0].status", is(EvseStatus.Available.name()))
                .body("data.chargingStation.evses[0].availabilityStatus", is(AvailabilityStatus.Operative.name()));
    }

    @Test
    @DisplayName("Should return all charging stations")
    public void testGetChargingStationsBasicQuery() {
        given()
                .contentType(ContentType.JSON)
                .body(CHARGING_STATIONS_BASIC_QUERY)
                .when()
                .post(GRAPHQL_ENDPOINT)
                .then()
                .statusCode(HttpResponseStatus.OK.code())
                .body("errors", nullValue())
                .body("data.chargingStations", notNullValue())
                .body("data.chargingStations.size()", greaterThanOrEqualTo(2))
                .body("data.chargingStations[0].id", is("-1"))
                .body("data.chargingStations[0].name", is("CP0"))
                .body("data.chargingStations[1].id", is("-2"))
                .body("data.chargingStations[1].name", is("CP1"));
    }

    @Test
    @DisplayName("Should return charging stations filtered by powerGridId")
    public void testGetChargingStationsWithPowerGridFilter() {
        String filterQuery = String.format(CHARGING_STATIONS_WITH_FILTER_QUERY, "powerGridId:-1");
        given()
                .contentType(ContentType.JSON)
                .body(filterQuery)
                .when()
                .post(GRAPHQL_ENDPOINT)
                .then()
                .statusCode(HttpResponseStatus.OK.code())
                .body("errors", nullValue())
                .body("data.chargingStations", notNullValue())
                .body("data.chargingStations.size()", greaterThanOrEqualTo(2))
                .body("data.chargingStations[0].id", is("-1"))
                .body("data.chargingStations[0].name", is("CP0"))
                .body("data.chargingStations[1].id", is("-2"))
                .body("data.chargingStations[1].name", is("CP1"));
    }

    @Test
    @DisplayName("Should return empty list for non-existing powerGridId filter")
    public void testGetChargingStationsWithNonExistingPowerGridFilter() {
        String filterQuery = String.format(CHARGING_STATIONS_WITH_FILTER_QUERY, "powerGridId:999");
        given()
                .contentType(ContentType.JSON)
                .body(filterQuery)
                .when()
                .post(GRAPHQL_ENDPOINT)
                .then()
                .statusCode(HttpResponseStatus.OK.code())
                .body("errors", nullValue())
                .body("data.chargingStations", notNullValue())
                .body("data.chargingStations", hasSize(0));
    }

    @Test
    @DisplayName("Should return charging stations with nested relationships")
    public void testGetChargingStationsWithNestedFields() {
        given()
                .contentType(ContentType.JSON)
                .body(CHARGING_STATIONS_WITH_NESTED_QUERY)
                .when()
                .post(GRAPHQL_ENDPOINT)
                .then()
                .statusCode(HttpResponseStatus.OK.code())
                .body("errors", nullValue())
                .body("data.chargingStations", notNullValue())
                .body("data.chargingStations.size()", greaterThanOrEqualTo(2))
                .body("data.chargingStations[0].id", is("-1"))
                .body("data.chargingStations[0].name", is("CP0"))
                .body("data.chargingStations[0].evses", hasSize(1))
                .body("data.chargingStations[0].evses[0].id", is("-1"))
                .body("data.chargingStations[0].evses[0].chargingStationEvseId", is(1));
    }

    @Test
    @DisplayName("Should return charging stations with locationId filter")
    public void testGetChargingStationsWithLocationFilter() {
        String filterQuery = String.format(CHARGING_STATIONS_WITH_FILTER_QUERY, "locationId:-1");
        given()
                .contentType(ContentType.JSON)
                .body(filterQuery)
                .when()
                .post(GRAPHQL_ENDPOINT)
                .then()
                .statusCode(HttpResponseStatus.OK.code())
                .body("errors", nullValue())
                .body("data.chargingStations", notNullValue())
                .body("data.chargingStations.size()", greaterThanOrEqualTo(2))
                .body("data.chargingStations[0].id", is("-1"))
                .body("data.chargingStations[1].id", is("-2"));
    }

    @Test
    @DisplayName("Should return charging stations with combined filters")
    public void testGetChargingStationsWithCombinedFilters() {
        String filterQuery = String.format(CHARGING_STATIONS_WITH_FILTER_QUERY, "locationId:-1,powerGridId:-1");
        given()
                .contentType(ContentType.JSON)
                .body(filterQuery)
                .when()
                .post(GRAPHQL_ENDPOINT)
                .then()
                .statusCode(HttpResponseStatus.OK.code())
                .body("errors", nullValue())
                .body("data.chargingStations", notNullValue())
                .body("data.chargingStations.size()", greaterThanOrEqualTo(2));
    }

    @Test
    @DisplayName("Should return all charging stations when empty filter provided")
    public void testGetChargingStationsWithEmptyFilter() {
        String filterQuery = String.format(CHARGING_STATIONS_WITH_FILTER_QUERY, "");
        given()
                .contentType(ContentType.JSON)
                .body(filterQuery)
                .when()
                .post(GRAPHQL_ENDPOINT)
                .then()
                .statusCode(HttpResponseStatus.OK.code())
                .body("errors", nullValue())
                .body("data.chargingStations", notNullValue())
                .body("data.chargingStations.size()", greaterThanOrEqualTo(2));
    }

    @Test
    @DisplayName("Should register a new charging station with connection url and password")
    public void testRegisterChargingStationBasic() {
        String newChargingStationName = "TestStation1";
        String mutationQuery = String.format(REGISTER_CHARGING_STATION_MUTATION, newChargingStationName);

        given()
                .contentType(ContentType.JSON)
                .body(mutationQuery)
                .when()
                .post(GRAPHQL_ENDPOINT)
                .then()
                .statusCode(HttpResponseStatus.OK.code())
                .body("errors", nullValue())
                .body("data.registerChargingStation", notNullValue())
                .body("data.registerChargingStation.id", notNullValue())
                .body("data.registerChargingStation.name", is(newChargingStationName))
                .body("data.registerChargingStation.status", is(ChargingStationStatus.New.name()))
                .body("data.registerChargingStation.availabilityStatus", is(AvailabilityStatus.Unknown.name()))
                .body("data.registerChargingStation.password", notNullValue())
                .body("data.registerChargingStation.url", notNullValue());
    }

    @Test
    @DisplayName("Should register charging station and verify it can be queried")
    public void testRegisterChargingStationAndQuery() {
        // First, register a new charging station
        String newChargingStationName = "QueryTestStation";
        String mutationQuery = String.format(REGISTER_CHARGING_STATION_MUTATION, newChargingStationName);

        String newStationId = given()
                .contentType(ContentType.JSON)
                .body(mutationQuery)
                .when()
                .post(GRAPHQL_ENDPOINT)
                .then()
                .statusCode(HttpResponseStatus.OK.code())
                .body("errors", nullValue())
                .body("data.registerChargingStation.name", is(newChargingStationName))
                .extract()
                .path("data.registerChargingStation.id");

        // Then, query the newly created charging station
        String queryString = String.format(CHARGING_STATION_SIMPLE_QUERY, Long.parseLong(newStationId));
        given()
                .contentType(ContentType.JSON)
                .body(queryString)
                .when()
                .post(GRAPHQL_ENDPOINT)
                .then()
                .statusCode(HttpResponseStatus.OK.code())
                .body("errors", nullValue())
                .body("data.chargingStation", notNullValue())
                .body("data.chargingStation.id", is(newStationId))
                .body("data.chargingStation.name", is(newChargingStationName));
    }

    @Test
    @DisplayName("Should update existing charging station name successfully")
    public void testUpdateChargingStationBasic() {
        Integer existingChargingStationId = -1;
        String newName = "UpdatedCP0";
        String mutationQuery = String.format(UPDATE_CHARGING_STATION_MUTATION, existingChargingStationId, newName);

        given()
                .contentType(ContentType.JSON)
                .body(mutationQuery)
                .when()
                .post(GRAPHQL_ENDPOINT)
                .then()
                .statusCode(HttpResponseStatus.OK.code())
                .body("errors", nullValue())
                .body("data.updateChargingStation", notNullValue())
                .body("data.updateChargingStation.id", is(existingChargingStationId.toString()))
                .body("data.updateChargingStation.name", is(newName))
                .body("data.updateChargingStation.status", is(ChargingStationStatus.Offline.name()))
                .body("data.updateChargingStation.availabilityStatus", is(AvailabilityStatus.Unknown.name()))
                .body("data.updateChargingStation.url", notNullValue())
                .body("data.updateChargingStation.userName", notNullValue())
                .body("data.updateChargingStation.password", notNullValue());
    }

    @Test
    @DisplayName("Should update charging station and verify change persists")
    public void testUpdateChargingStationAndVerify() {
        Integer existingChargingStationId = -2;
        String originalName = "CP1";
        String updatedName = "UpdatedCP1";

        // First, verify the original name
        String queryString = String.format(CHARGING_STATION_SIMPLE_QUERY, existingChargingStationId);
        given()
                .contentType(ContentType.JSON)
                .body(queryString)
                .when()
                .post(GRAPHQL_ENDPOINT)
                .then()
                .statusCode(HttpResponseStatus.OK.code())
                .body("data.chargingStation.name", is(originalName));

        // Update the charging station name
        String mutationQuery = String.format(UPDATE_CHARGING_STATION_MUTATION, existingChargingStationId, updatedName);
        given()
                .contentType(ContentType.JSON)
                .body(mutationQuery)
                .when()
                .post(GRAPHQL_ENDPOINT)
                .then()
                .statusCode(HttpResponseStatus.OK.code())
                .body("errors", nullValue())
                .body("data.updateChargingStation.name", is(updatedName));

        // Verify the change persists by querying again
        given()
                .contentType(ContentType.JSON)
                .body(queryString)
                .when()
                .post(GRAPHQL_ENDPOINT)
                .then()
                .statusCode(HttpResponseStatus.OK.code())
                .body("data.chargingStation.name", is(updatedName));
    }

    @Test
    @DisplayName("Should update charging station with null name")
    public void testUpdateChargingStationWithNullName() {
        Integer existingChargingStationId = -1;
        String mutationQuery = String.format(UPDATE_CHARGING_STATION_MUTATION_NULL_NAME, existingChargingStationId);

        given()
                .contentType(ContentType.JSON)
                .body(mutationQuery)
                .when()
                .post(GRAPHQL_ENDPOINT)
                .then()
                .statusCode(HttpResponseStatus.OK.code())
                .body("errors", nullValue())
                .body("data.updateChargingStation", notNullValue())
                .body("data.updateChargingStation.id", is(existingChargingStationId.toString()));
    }

    @Test
    @DisplayName("Should update charging station with empty string name")
    public void testUpdateChargingStationWithEmptyName() {
        Integer existingChargingStationId = -1;
        String emptyName = "";
        String mutationQuery = String.format(UPDATE_CHARGING_STATION_MUTATION, existingChargingStationId, emptyName);

        given()
                .contentType(ContentType.JSON)
                .body(mutationQuery)
                .when()
                .post(GRAPHQL_ENDPOINT)
                .then()
                .statusCode(HttpResponseStatus.OK.code())
                .body("errors", nullValue())
                .body("data.updateChargingStation", notNullValue())
                .body("data.updateChargingStation.id", is(existingChargingStationId.toString()))
                .body("data.updateChargingStation.name", is(emptyName));
    }

    @Test
    @DisplayName("Should fail to update non-existing charging station")
    public void testUpdateNonExistingChargingStation() {
        Integer nonExistingId = 999999;
        String newName = "NonExistentStation";
        String mutationQuery = String.format(UPDATE_CHARGING_STATION_MUTATION, nonExistingId, newName);

        given()
                .contentType(ContentType.JSON)
                .body(mutationQuery)
                .when()
                .post(GRAPHQL_ENDPOINT)
                .then()
                .statusCode(HttpResponseStatus.OK.code())
                .body("errors", notNullValue())
                .body("errors.size()", greaterThanOrEqualTo(1))
                .body("data.updateChargingStation", nullValue());
    }

    @Test
    @DisplayName("Should update charging station with same name")
    public void testUpdateChargingStationWithSameName() {
        Integer existingChargingStationId = -1;
        String sameName = "CP0"; // This is the original name from test data
        String mutationQuery = String.format(UPDATE_CHARGING_STATION_MUTATION, existingChargingStationId, sameName);

        given()
                .contentType(ContentType.JSON)
                .body(mutationQuery)
                .when()
                .post(GRAPHQL_ENDPOINT)
                .then()
                .statusCode(HttpResponseStatus.OK.code())
                .body("errors", nullValue())
                .body("data.updateChargingStation", notNullValue())
                .body("data.updateChargingStation.id", is(existingChargingStationId.toString()))
                .body("data.updateChargingStation.name", is(sameName));
    }

    @Inject
    Vertx vertx;
    private WebClient client;

    @Test
    @DisplayName("Should update charging station with very long name")
    @TestReactiveTransaction
    public void testUpdateChargingStationWithLongName(UniAsserter asserter) throws SystemException {
        this.client = WebClient.create(vertx);
        Integer existingChargingStationId = -1;
        String longName = "A".repeat(255);
        String mutationQuery = String.format(UPDATE_CHARGING_STATION_MUTATION, existingChargingStationId, longName);
        asserter.execute(() -> {
            return client.post("http://localhost:8081" + GRAPHQL_ENDPOINT)
                    .sendBuffer(Buffer.buffer(mutationQuery))
                    .onSuccess(response -> {
                Assertions.assertEquals(200, response.statusCode());
                Assertions.assertNull(response.bodyAsJsonObject().getJsonArray("errors"));
                Assertions.assertNotNull(response.bodyAsJsonObject().getJsonObject("data").getJsonObject("updateChargingStation"));
                Assertions.assertEquals(existingChargingStationId.toString(), response.bodyAsJsonObject().getJsonObject("data").getJsonObject("updateChargingStation").getString("id"));
        });
        /*asserter.execute(() -> Panache.withSession(() -> {

            return Uni.createFrom().item(
                    given()
                            .contentType(ContentType.JSON)
                            .body(mutationQuery)
                            .when()
                            .post(GRAPHQL_ENDPOINT)
                            .then()
                            .statusCode(HttpResponseStatus.OK.code())
                            .body("errors", nullValue())
                            .body("data.updateChargingStation", notNullValue())
                            .body("data.updateChargingStation.id", is(existingChargingStationId.toString()))
                            .body("data.updateChargingStation.name", is(longName))
            );
        }));

         */
    }

    @Test
    @DisplayName("Should update charging station with special characters in name")
    public void testUpdateChargingStationWithSpecialCharacters() {
        Integer existingChargingStationId = -2;
        String specialName = "CP-2_Test@Station#2024!";
        String mutationQuery = String.format(UPDATE_CHARGING_STATION_MUTATION, existingChargingStationId, specialName);

        given()
                .contentType(ContentType.JSON)
                .body(mutationQuery)
                .when()
                .post(GRAPHQL_ENDPOINT)
                .then()
                .statusCode(HttpResponseStatus.OK.code())
                .body("errors", nullValue())
                .body("data.updateChargingStation", notNullValue())
                .body("data.updateChargingStation.id", is(existingChargingStationId.toString()))
                .body("data.updateChargingStation.name", is(specialName));
    }

}